import { Strategy } from 'passport-local';
import { AuthService } from "../auth.service";
declare const LocalStrategy_base: new (...args: [] | [options: import("passport-local").IStrategyOptionsWithRequest] | [options: import("passport-local").IStrategyOptions]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class LocalStrategy extends LocalStrategy_base {
    private authService;
    constructor(authService: AuthService);
    validate(email: string, password: string): Promise<{
        organizationId: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        role: import("generated/prisma").$Enums.UserRole;
        refreshToken: string | null;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
    }>;
}
export {};
