export declare enum UserRole {
    ADMIN = "ADMIN",
    PARTNER = "PARTNER",
    COORDINATOR = "COORDINATOR",
    LEGAL = "LEGAL",
    TECHNICAL_EXPERT = "TECHNICAL_EXPERT",
    HOD = "HOD",
    PS = "PS",
    MINISTER = "MINISTER"
}
export declare class RegisterDto {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    role: UserRole;
    organizationId?: string;
}
export declare class LoginDto {
    email: string;
    password: string;
}
export declare class RefreshTokenDto {
    refreshToken: string;
}
export declare class ForgotPasswordDto {
    email: string;
}
export declare class ResetPasswordDto {
    token: string;
    password: string;
}
export declare class VerifyEmailDto {
    token: string;
}
export declare class InviteUserDto {
    email: string;
    role: UserRole;
    organizationId?: string;
}
export declare class AcceptInvitationDto {
    token: string;
    firstName: string;
    lastName: string;
    password: string;
}
import { CreateOrganizationDto } from '../../organizations/dto';
export declare class RegisterPartnerWithOrganizationDto {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    organization: CreateOrganizationDto;
}
