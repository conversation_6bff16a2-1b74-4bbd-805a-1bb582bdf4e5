import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { User } from '../../generated/prisma';
import { PrismaService } from 'src/prisma/prisma.service';
import { EmailService } from 'src/email/email.service';
import { AcceptInvitationDto, ForgotPasswordDto, InviteUserDto, LoginDto, RefreshTokenDto, RegisterDto, ResetPasswordDto, VerifyEmailDto } from './dto';
export declare class AuthService {
    private jwtService;
    private prisma;
    private emailService;
    private configService;
    private readonly logger;
    private readonly SALT_ROUNDS;
    private readonly EMAIL_VERIFICATION_EXPIRY;
    private readonly PASSWORD_RESET_EXPIRY;
    private readonly REFRESH_TOKEN_EXPIRY;
    private readonly INVITATION_EXPIRY;
    constructor(jwtService: JwtService, prisma: PrismaService, emailService: EmailService, configService: ConfigService);
    generateAccessToken(user: User): Promise<string>;
    generateRefreshToken(user: User): Promise<string>;
    getUser(id: string): Promise<{
        organizationId: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        role: import("../../generated/prisma").$Enums.UserRole;
        refreshToken: string | null;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
    }>;
    validateUser(email: string, password: string): Promise<{
        organizationId: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        role: import("../../generated/prisma").$Enums.UserRole;
        refreshToken: string | null;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
    }>;
    register(registerDto: RegisterDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("../../generated/prisma").$Enums.UserRole;
            emailVerified: boolean;
            organizationId: string;
        };
        accessToken: string;
        refreshToken: string;
    }>;
    registerPartnerWithOrganization(registerData: any): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("../../generated/prisma").$Enums.UserRole;
            emailVerified: boolean;
            organizationId: string;
        };
        organization: {
            id: string;
            organizationName: string;
            organizationEmail: string;
        };
        accessToken: string;
        refreshToken: string;
    }>;
    private validateOrganizationData;
    login(loginDto: LoginDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("../../generated/prisma").$Enums.UserRole;
            emailVerified: boolean;
        };
        accessToken: string;
        refreshToken: string;
    }>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<{
        message: string;
    }>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        message: string;
    }>;
    inviteUser(inviteUserDto: InviteUserDto, inviterId: string): Promise<{
        message: string;
    }>;
    acceptInvitation(acceptInvitationDto: AcceptInvitationDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("../../generated/prisma").$Enums.UserRole;
            emailVerified: boolean;
            organization: {
                id: string;
                name: string;
            };
        };
        accessToken: string;
        refreshToken: string;
    }>;
    resendVerificationEmail(userId: string): Promise<{
        message: string;
    }>;
    logout(userId: string): Promise<{
        message: string;
    }>;
}
