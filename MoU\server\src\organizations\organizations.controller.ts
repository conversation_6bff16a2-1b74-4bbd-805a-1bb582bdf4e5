import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto, OrganizationResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';

@ApiTags('organizations')
@Controller('organizations')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class OrganizationsController {
  constructor(private readonly organizationsService: OrganizationsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all organizations (Admin sees all, others see their own)' })
  @ApiResponse({ status: 200, description: 'Returns list of organizations', type: [OrganizationResponseDto] })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  async findAll(@Request() req: any) {
    return this.organizationsService.findAll(req.user.sub);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get organization by ID' })
  @ApiParam({ name: 'id', description: 'Organization ID' })
  @ApiResponse({ status: 200, description: 'Returns organization details', type: OrganizationResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.organizationsService.findOne(id, req.user.sub);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new organization (Admin only)' })
  @ApiResponse({ status: 201, description: 'Organization created successfully', type: OrganizationResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data or business rule violation' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Organization type not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Organization with ID or email already exists' })
  async create(@Body() createOrganizationDto: CreateOrganizationDto, @Request() req: any) {
    return this.organizationsService.create(createOrganizationDto, req.user.sub);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an organization' })
  @ApiParam({ name: 'id', description: 'Organization ID' })
  @ApiResponse({ status: 200, description: 'Organization updated successfully', type: OrganizationResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Organization with ID or email already exists' })
  async update(@Param('id') id: string, @Body() updateOrganizationDto: UpdateOrganizationDto, @Request() req: any) {
    return this.organizationsService.update(id, updateOrganizationDto, req.user.sub);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an organization (Admin only)' })
  @ApiParam({ name: 'id', description: 'Organization ID' })
  @ApiResponse({ status: 200, description: 'Organization deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - Cannot delete organization with active users' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.organizationsService.remove(id, req.user.sub);
  }
}
