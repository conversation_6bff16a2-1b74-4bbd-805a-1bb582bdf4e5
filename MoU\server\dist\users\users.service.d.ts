import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto, UpdateUserDto } from './dto';
export declare class UsersService {
    private prisma;
    private readonly logger;
    private readonly SALT_ROUNDS;
    constructor(prisma: PrismaService);
    findAll(currentUserId: string): Promise<{
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        role: import("generated/prisma").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: string;
        organization: {
            organizationName: string;
            id: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    findOne(id: string, currentUserId: string): Promise<{
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        role: import("generated/prisma").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: string;
        organization: {
            organizationName: string;
            id: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }>;
    create(createUserDto: CreateUserDto, currentUserId: string): Promise<{
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        role: import("generated/prisma").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: string;
        organization: {
            organizationName: string;
            id: string;
        };
        createdAt: Date;
        updatedAt: Date;
        tempPassword: string;
    }>;
    update(id: string, updateUserDto: UpdateUserDto, currentUserId: string): Promise<{
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        role: import("generated/prisma").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: string;
        organization: {
            organizationName: string;
            id: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }>;
    remove(id: string, currentUserId: string): Promise<{
        message: string;
    }>;
}
