import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AddressesService } from './addresses.service';
import { CreateAddressDto, UpdateAddressDto, AddressResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';

@ApiTags('addresses')
@Controller('addresses')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class AddressesController {
  constructor(private readonly addressesService: AddressesService) {}

  @Get()
  @ApiOperation({ summary: 'Get addresses by organization ID' })
  @ApiQuery({ name: 'organizationId', description: 'Organization ID to filter addresses' })
  @ApiResponse({ status: 200, description: 'Returns addresses for the organization', type: [AddressResponseDto] })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  async findByOrganization(@Query('organizationId') organizationId: string) {
    return this.addressesService.findByOrganization(organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get address by ID' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({ status: 200, description: 'Returns address details', type: AddressResponseDto })
  @ApiResponse({ status: 404, description: 'Address not found' })
  async findOne(@Param('id') id: string) {
    return this.addressesService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new address for an organization' })
  @ApiResponse({ status: 201, description: 'Address created successfully', type: AddressResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data or business rule violation' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Address type already exists for organization' })
  async create(@Body() createAddressDto: CreateAddressDto, @Request() req: any) {
    return this.addressesService.create(createAddressDto, req.user.sub);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an address' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({ status: 200, description: 'Address updated successfully', type: AddressResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data or business rule violation' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Address not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Address type already exists for organization' })
  async update(@Param('id') id: string, @Body() updateAddressDto: UpdateAddressDto, @Request() req: any) {
    return this.addressesService.update(id, updateAddressDto, req.user.sub);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an address' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({ status: 200, description: 'Address deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - Cannot delete mandatory Rwanda address' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Address not found' })
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.addressesService.remove(id, req.user.sub);
  }
}
