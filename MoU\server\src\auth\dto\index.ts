import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, IsEnum, IsOptional, Matches, MinLength, ValidateNested, IsInt, IsArray, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';

export enum UserRole {
    ADMIN = 'ADMIN',
    PARTNER = 'PARTNER',
    COORDINATOR = 'COORDINATOR',
    LEGAL = 'LEGAL',
    TECHNICAL_EXPERT = 'TECHNICAL_EXPERT',
    HOD = 'HOD',
    PS = 'PS',
    MINISTER = 'MINISTER',
}

export enum AddressType {
    HEADQUARTERS = 'HEADQUARTERS',
    RWANDA = 'RWANDA'
}

// Address DTO for registration
export class CreateAddressForRegistrationDto {
    @ApiProperty({ enum: AddressType, description: 'Type of address (HEADQUARTERS or RWANDA)' })
    @IsNotEmpty()
    @IsEnum(AddressType)
    addressType: AddressType;

    @ApiProperty({ description: 'Country name' })
    @IsNotEmpty()
    @IsString()
    country: string;

    @ApiProperty({ required: false, description: 'Province/State (optional for headquarters addresses)' })
    @IsOptional()
    @IsString()
    province?: string;

    @ApiProperty({ required: false, description: 'District (optional for headquarters addresses)' })
    @IsOptional()
    @IsString()
    district?: string;

    @ApiProperty({ required: false, description: 'Sector (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    sector?: string;

    @ApiProperty({ required: false, description: 'Cell (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    cell?: string;

    @ApiProperty({ required: false, description: 'Village (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    village?: string;

    @ApiProperty({ description: 'Street address' })
    @IsNotEmpty()
    @IsString()
    street: string;

    @ApiProperty({ required: false, description: 'Avenue' })
    @IsOptional()
    @IsString()
    avenue?: string;

    @ApiProperty({ description: 'P.O. Box' })
    @IsNotEmpty()
    @IsString()
    poBox: string;

    @ApiProperty({ required: false, description: 'Postal code' })
    @IsOptional()
    @IsString()
    postalCode?: string;
}

// Organization DTO for registration
export class CreateOrganizationForRegistrationDto {
    @ApiProperty({ description: 'Organization name' })
    @IsNotEmpty()
    @IsString()
    organizationName: string;

    @ApiProperty({ description: 'Organization business registration number' })
    @IsNotEmpty()
    @IsString()
    organizationRegistrationNumber: string;

    @ApiProperty({ description: 'Organization phone number' })
    @IsNotEmpty()
    @IsString()
    organizationPhoneNumber: string;

    @ApiProperty({ description: 'Organization email address' })
    @IsNotEmpty()
    @IsEmail()
    organizationEmail: string;

    @ApiProperty({ required: false, description: 'Organization website URL' })
    @IsOptional()
    @IsString()
    organizationWebsite?: string;

    @ApiProperty({ description: 'Home country representative name' })
    @IsNotEmpty()
    @IsString()
    homeCountryRepresentative: string;

    @ApiProperty({ description: 'Rwanda representative name' })
    @IsNotEmpty()
    @IsString()
    rwandaRepresentative: string;

    @ApiProperty({ description: 'Organization RGB number' })
    @IsNotEmpty()
    @IsString()
    organizationRgbNumber: string;

    @ApiProperty({ description: 'Organization type ID' })
    @IsNotEmpty()
    @IsInt()
    organizationTypeId: number;

    @ApiProperty({
        type: [CreateAddressForRegistrationDto],
        description: 'Organization addresses (max 2: 1 headquarters, 1 Rwanda). Rwanda address is mandatory.',
        maxItems: 2
    })
    @IsArray()
    @ArrayMaxSize(2)
    @ValidateNested({ each: true })
    @Type(() => CreateAddressForRegistrationDto)
    addresses: CreateAddressForRegistrationDto[];
}

// DTO for PARTNER user self-registration with organization creation
export class RegisterDto {
    @ApiProperty({ description: 'User first name' })
    @IsNotEmpty()
    @IsString()
    firstName: string;

    @ApiProperty({ description: 'User last name' })
    @IsNotEmpty()
    @IsString()
    lastName: string;

    @ApiProperty({ description: 'User email address' })
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @ApiProperty({ description: 'User password' })
    @IsNotEmpty()
    @IsString()
    @MinLength(8)
    @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: 'Password must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number or special character',
    })
    password: string;

    @ApiProperty({ description: 'Organization details' })
    @ValidateNested()
    @Type(() => CreateOrganizationForRegistrationDto)
    organization: CreateOrganizationForRegistrationDto;
}

// DTO for ADMIN-created users (non-PARTNER roles)
export class CreateUserByAdminDto {
    @ApiProperty({ description: 'User first name' })
    @IsNotEmpty()
    @IsString()
    firstName: string;

    @ApiProperty({ description: 'User last name' })
    @IsNotEmpty()
    @IsString()
    lastName: string;

    @ApiProperty({ description: 'User email address' })
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @ApiProperty({ description: 'User role (cannot be PARTNER)' })
    @IsNotEmpty()
    @IsEnum(UserRole)
    role: UserRole;

    @ApiProperty({ required: false, description: 'Organization ID (only for non-ADMIN roles)' })
    @IsOptional()
    @IsString()
    organizationId?: string;
}

export class LoginDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    password: string;
}

export class RefreshTokenDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    refreshToken: string;
}

export class ForgotPasswordDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsEmail()
    email: string;
}

export class ResetPasswordDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    token: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    @MinLength(8)
    @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: 'Password must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number or special character',
    })
    password: string;
}

export class VerifyEmailDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    token: string;
}

export class InviteUserDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsEnum(UserRole)
    role: UserRole;

    @ApiProperty()
    @IsOptional()
    @IsString()
    organizationId?: string;
}

export class AcceptInvitationDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    token: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    firstName: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    lastName: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    @MinLength(8)
    @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: 'Password must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number or special character',
    })
    password: string;
}


