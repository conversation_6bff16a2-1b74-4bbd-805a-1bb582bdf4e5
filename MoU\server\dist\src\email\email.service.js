"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const mailer_1 = require("@nestjs-modules/mailer");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let EmailService = EmailService_1 = class EmailService {
    constructor(mailerService, configService) {
        this.mailerService = mailerService;
        this.configService = configService;
        this.logger = new common_1.Logger(EmailService_1.name);
    }
    getBaseContext() {
        return {
            year: new Date().getFullYear(),
            logoUrl: this.configService.get('LOGO_URL') || 'https://www.moh.gov.rw/assets/logo.png',
            supportEmail: this.configService.get('SUPPORT_EMAIL') || '<EMAIL>',
            supportPhone: this.configService.get('SUPPORT_PHONE') || '+*********** 000',
            frontendUrl: this.configService.get('FRONTEND_URL') || 'http://localhost:3000',
            socialLinks: {
                twitter: this.configService.get('TWITTER_URL') || 'https://twitter.com/RwandaHealth',
                facebook: this.configService.get('FACEBOOK_URL') || 'https://facebook.com/RwandaHealth',
                linkedin: this.configService.get('LINKEDIN_URL') || 'https://linkedin.com/company/ministry-of-health-rwanda'
            },
            unsubscribeUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/email-preferences/unsubscribe`,
            preferencesUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/email-preferences`
        };
    }
    async sendWelcomeEmail(user) {
        try {
            const context = {
                ...this.getBaseContext(),
                firstName: user.firstName,
                lastName: user.lastName,
                name: `${user.firstName} ${user.lastName}`,
                verificationRequired: !!user.verificationToken,
                verificationUrl: user.verificationToken ?
                    `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/auth/verify-email?token=${user.verificationToken}` :
                    undefined,
                dashboardUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/dashboard`
            };
            await this.mailerService.sendMail({
                to: user.email,
                subject: 'Welcome to MoU Management System - Ministry of Health',
                template: 'welcome',
                context
            });
            this.logger.log(`Welcome email sent to ${user.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to send welcome email to ${user.email}`, error.stack);
            throw error;
        }
    }
    async sendVerificationEmail(user) {
        try {
            const context = {
                ...this.getBaseContext(),
                firstName: user.firstName,
                lastName: user.lastName,
                name: `${user.firstName} ${user.lastName}`,
                verificationUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/auth/verify-email?token=${user.verificationToken}`
            };
            await this.mailerService.sendMail({
                to: user.email,
                subject: 'Verify Your Email Address',
                template: 'verify-email',
                context
            });
            this.logger.log(`Verification email sent to ${user.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to send verification email to ${user.email}`, error.stack);
            throw error;
        }
    }
    async sendPasswordResetEmail(user) {
        try {
            const context = {
                ...this.getBaseContext(),
                firstName: user.firstName,
                lastName: user.lastName,
                name: `${user.firstName} ${user.lastName}`,
                resetUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/auth/reset-password?token=${user.resetToken}`
            };
            await this.mailerService.sendMail({
                to: user.email,
                subject: 'Reset Your Password',
                template: 'reset-password',
                context
            });
            this.logger.log(`Password reset email sent to ${user.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to send password reset email to ${user.email}`, error.stack);
            throw error;
        }
    }
    async sendInvitationEmail(invitation) {
        try {
            const context = {
                ...this.getBaseContext(),
                inviterName: invitation.inviterName,
                companyName: invitation.companyName,
                companyDescription: invitation.companyDescription || `Join ${invitation.companyName} on Supportive`,
                role: invitation.role,
                invitationUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/auth/accept-invitation?token=${invitation.invitationToken}`
            };
            await this.mailerService.sendMail({
                to: invitation.email,
                subject: `You've Been Invited to Join ${invitation.companyName} on Supportive`,
                template: 'invitation',
                context
            });
            this.logger.log(`Invitation email sent to ${invitation.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to send invitation email to ${invitation.email}`, error.stack);
            throw error;
        }
    }
    async sendHostBookingNotification(booking) {
        try {
            const context = {
                ...this.getBaseContext(),
                ...booking,
                approveUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/host/bookings/${booking.bookingId}/approve`,
                rejectUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/host/bookings/${booking.bookingId}/reject`,
                dashboardUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/host/dashboard`
            };
            await this.mailerService.sendMail({
                to: booking.hostEmail,
                subject: `New Booking Request: ${booking.propertyName}`,
                template: 'new-booking',
                context
            });
            this.logger.log(`Booking notification email sent to ${booking.hostEmail}`);
        }
        catch (error) {
            this.logger.error(`Failed to send booking notification email to ${booking.hostEmail}`, error.stack);
            throw error;
        }
    }
    async sendBookingApprovalNotification(booking) {
        try {
            const context = {
                ...this.getBaseContext(),
                ...booking,
                bookingDetailsUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/trips/${booking.bookingId}`,
                tripsUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/trips`,
                cancellationPolicyUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/policies/cancellation`
            };
            await this.mailerService.sendMail({
                to: booking.guestEmail,
                subject: 'Booking Confirmed! Your Reservation Details',
                template: 'approve',
                context
            });
            this.logger.log(`Booking approval email sent to ${booking.guestEmail}`);
        }
        catch (error) {
            this.logger.error(`Failed to send booking approval email to ${booking.guestEmail}`, error.stack);
            throw error;
        }
    }
    async sendBookingRejectionNotification(booking) {
        try {
            const context = {
                ...this.getBaseContext(),
                ...booking,
                searchUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/search?checkin=${booking.checkIn}&checkout=${booking.checkOut}&guests=${booking.guests}`
            };
            await this.mailerService.sendMail({
                to: booking.guestEmail,
                subject: 'Update on Your Booking Request',
                template: 'reject',
                context
            });
            this.logger.log(`Booking rejection email sent to ${booking.guestEmail}`);
        }
        catch (error) {
            this.logger.error(`Failed to send booking rejection email to ${booking.guestEmail}`, error.stack);
            throw error;
        }
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = EmailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [mailer_1.MailerService,
        config_1.ConfigService])
], EmailService);
//# sourceMappingURL=email.service.js.map