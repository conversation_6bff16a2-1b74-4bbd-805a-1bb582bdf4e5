"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const mailer_1 = require("@nestjs-modules/mailer");
const path_1 = require("path");
const handlebars_adapter_1 = require("@nestjs-modules/mailer/dist/adapters/handlebars.adapter");
const auth_module_1 = require("./auth/auth.module");
const prisma_module_1 = require("./prisma/prisma.module");
const config_1 = require("@nestjs/config");
const email_module_1 = require("./email/email.module");
const core_1 = require("@nestjs/core");
const jwt_auth_guard_1 = require("./auth/guard/jwt-auth.guard");
const notifications_module_1 = require("./notifications/notifications.module");
const users_module_1 = require("./users/users.module");
const organization_types_module_1 = require("./organization-types/organization-types.module");
const organizations_module_1 = require("./organizations/organizations.module");
const addresses_module_1 = require("./addresses/addresses.module");
const budget_types_module_1 = require("./budget-types/budget-types.module");
const funding_sources_module_1 = require("./funding-sources/funding-sources.module");
const funding_units_module_1 = require("./funding-units/funding-units.module");
const serve_static_1 = require("@nestjs/serve-static");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mailer_1.MailerModule.forRoot({
                transport: {
                    host: 'localhost',
                    port: 1025,
                    ignoreTLS: true,
                    secure: false,
                },
                defaults: {
                    from: '"MoU Management System" <<EMAIL>>',
                },
                template: {
                    dir: (0, path_1.join)(__dirname, 'email/templates'),
                    adapter: new handlebars_adapter_1.HandlebarsAdapter(),
                    options: {
                        strict: true,
                    },
                },
            }),
            config_1.ConfigModule.forRoot({
                cache: true,
                isGlobal: true
            }),
            serve_static_1.ServeStaticModule.forRoot({
                rootPath: (0, path_1.join)(__dirname, '..', 'uploads'),
                serveRoot: '/uploads',
            }),
            prisma_module_1.PrismaModule,
            auth_module_1.AuthModule,
            email_module_1.EmailModule,
            users_module_1.UsersModule,
            organization_types_module_1.OrganizationTypesModule,
            organizations_module_1.OrganizationsModule,
            addresses_module_1.AddressesModule,
            budget_types_module_1.BudgetTypesModule,
            funding_sources_module_1.FundingSourcesModule,
            funding_units_module_1.FundingUnitsModule,
            notifications_module_1.NotificationsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            {
                provide: core_1.APP_GUARD,
                useClass: jwt_auth_guard_1.JwtGuard
            }
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map