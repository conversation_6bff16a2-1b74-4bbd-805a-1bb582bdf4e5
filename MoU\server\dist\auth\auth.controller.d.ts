import { AuthService } from './auth.service';
import { AcceptInvitationDto, ForgotPasswordDto, InviteUserDto, LoginDto, RefreshTokenDto, RegisterDto, CreateUserByAdminDto, ResetPasswordDto, VerifyEmailDto } from './dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("generated/prisma").$Enums.UserRole;
            emailVerified: boolean;
        };
        accessToken: string;
        refreshToken: string;
    }>;
    register(registerDto: RegisterDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("generated/prisma").$Enums.UserRole;
            emailVerified: boolean;
            organizationId: string;
        };
        organization: {
            id: string;
            organizationName: string;
            organizationEmail: string;
        };
        accessToken: string;
        refreshToken: string;
    }>;
    createUser(createUserDto: CreateUserByAdminDto, req: any): Promise<{
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        role: import("generated/prisma").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: string;
        organization: {
            organizationName: string;
            id: string;
        };
        createdAt: Date;
        updatedAt: Date;
        tempPassword: string;
    }>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<{
        message: string;
    }>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        message: string;
    }>;
    inviteUser(inviteUserDto: InviteUserDto, req: any): Promise<{
        message: string;
    }>;
    acceptInvitation(acceptInvitationDto: AcceptInvitationDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: import("generated/prisma").$Enums.UserRole;
            emailVerified: boolean;
            organization: {
                id: string;
                name: string;
            };
        };
        accessToken: string;
        refreshToken: string;
    }>;
    resendVerification(req: any): Promise<{
        message: string;
    }>;
    logout(req: any): Promise<{
        message: string;
    }>;
    me(req: any): Promise<{
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: import("generated/prisma").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: string;
    }>;
}
