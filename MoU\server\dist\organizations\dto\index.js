"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrganizationWithUserDto = exports.OrganizationResponseDto = exports.UpdateOrganizationDto = exports.CreateOrganizationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const dto_1 = require("../../addresses/dto");
class CreateOrganizationDto {
}
exports.CreateOrganizationDto = CreateOrganizationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization name' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "organizationName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization business registration number' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "organizationRegistrationNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization phone number' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "organizationPhoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization email address' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "organizationEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization website URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "organizationWebsite", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Home country representative name' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "homeCountryRepresentative", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Rwanda representative name' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "rwandaRepresentative", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization RGB number' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "organizationRgbNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization type ID' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateOrganizationDto.prototype, "organizationTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: [dto_1.CreateAddressDto],
        description: 'Organization addresses (max 2: 1 headquarters, 1 Rwanda). Rwanda address is mandatory.',
        maxItems: 2
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMaxSize)(2),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => dto_1.CreateAddressDto),
    __metadata("design:type", Array)
], CreateOrganizationDto.prototype, "addresses", void 0);
class UpdateOrganizationDto {
}
exports.UpdateOrganizationDto = UpdateOrganizationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization name' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "organizationName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization business registration number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "organizationRegistrationNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization phone number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "organizationPhoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization email address' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "organizationEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization website URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "organizationWebsite", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Home country representative name' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "homeCountryRepresentative", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Rwanda representative name' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "rwandaRepresentative", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization RGB number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "organizationRgbNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Organization type ID' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], UpdateOrganizationDto.prototype, "organizationTypeId", void 0);
class OrganizationResponseDto {
}
exports.OrganizationResponseDto = OrganizationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "organizationName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "organizationRegistrationNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "organizationPhoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "organizationEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "organizationWebsite", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "homeCountryRepresentative", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "rwandaRepresentative", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], OrganizationResponseDto.prototype, "organizationRgbNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], OrganizationResponseDto.prototype, "organizationTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [dto_1.AddressResponseDto] }),
    __metadata("design:type", Array)
], OrganizationResponseDto.prototype, "addresses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], OrganizationResponseDto.prototype, "createAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], OrganizationResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], OrganizationResponseDto.prototype, "deleted", void 0);
class CreateOrganizationWithUserDto {
}
exports.CreateOrganizationWithUserDto = CreateOrganizationWithUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User first name' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationWithUserDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User last name' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationWithUserDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User email address' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateOrganizationWithUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User password' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationWithUserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization details' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CreateOrganizationDto),
    __metadata("design:type", CreateOrganizationDto)
], CreateOrganizationWithUserDto.prototype, "organization", void 0);
//# sourceMappingURL=index.js.map