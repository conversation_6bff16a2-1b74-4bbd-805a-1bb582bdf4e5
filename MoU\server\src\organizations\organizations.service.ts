import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from './dto';
import { AddressType } from '../addresses/dto';

@Injectable()
export class OrganizationsService {
    private readonly logger = new Logger(OrganizationsService.name);

    constructor(private prisma: PrismaService) {}

    async findAll(currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // ADMIN can see all organizations, others can only see their own
            const whereClause = currentUser.role === 'ADMIN' 
                ? { deleted: false }
                : { 
                    deleted: false,
                    id: currentUser.organizationId || 'no-org' // If no org, return empty
                };

            const organizations = await this.prisma.organization.findMany({
                where: whereClause,
                include: {
                    addresses: {
                        where: { deleted: false },
                        orderBy: { addressType: 'asc' }
                    },
                    organizationType: true
                },
                orderBy: { createAt: 'desc' }
            });

            return organizations;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error('Failed to fetch organizations', error.stack);
            throw new Error('Failed to fetch organizations');
        }
    }

    async findOne(id: string, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            const organization = await this.prisma.organization.findFirst({
                where: { 
                    id,
                    deleted: false 
                },
                include: {
                    addresses: {
                        where: { deleted: false },
                        orderBy: { addressType: 'asc' }
                    },
                    organizationType: true
                }
            });

            if (!organization) {
                throw new NotFoundException('Organization not found');
            }

            // Check permissions: ADMIN can see all, others can only see their own
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== id) {
                throw new ForbiddenException('You can only view your own organization');
            }

            return organization;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to fetch organization ${id}`, error.stack);
            throw new Error('Failed to fetch organization');
        }
    }

    async create(createOrganizationDto: CreateOrganizationDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create organizations directly
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create organizations');
            }

            // Validate business rules
            await this.validateOrganizationData(createOrganizationDto);

            // Validate addresses
            this.validateAddresses(createOrganizationDto.addresses);

            // Check if organization with same ID or email already exists
            const existingOrg = await this.prisma.organization.findFirst({
                where: {
                    OR: [
                        { organizationId: createOrganizationDto.organizationId },
                        { organizationEmail: createOrganizationDto.organizationEmail }
                    ],
                    deleted: false
                }
            });

            if (existingOrg) {
                throw new ConflictException('Organization with this ID or email already exists');
            }

            // Create organization with addresses in a transaction
            const organization = await this.prisma.$transaction(async (prisma) => {
                // Create organization
                const newOrg = await prisma.organization.create({
                    data: {
                        organizationName: createOrganizationDto.organizationName,
                        organizationId: createOrganizationDto.organizationId,
                        organizationPhoneNumber: createOrganizationDto.organizationPhoneNumber,
                        organizationEmail: createOrganizationDto.organizationEmail,
                        organizationWebsite: createOrganizationDto.organizationWebsite,
                        homeCountryRepresentative: createOrganizationDto.homeCountryRepresentative,
                        rwandaRepresentative: createOrganizationDto.rwandaRepresentative,
                        organizationRgbNumber: createOrganizationDto.organizationRgbNumber,
                        organizationTypeId: createOrganizationDto.organizationTypeId
                    }
                });

                // Create addresses
                for (const addressData of createOrganizationDto.addresses) {
                    await prisma.address.create({
                        data: {
                            ...addressData,
                            organizationId: newOrg.id
                        }
                    });
                }

                // Return organization with addresses
                return await prisma.organization.findUnique({
                    where: { id: newOrg.id },
                    include: {
                        addresses: {
                            where: { deleted: false },
                            orderBy: { addressType: 'asc' }
                        },
                        organizationType: true
                    }
                });
            });

            return organization;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create organization', error.stack);
            throw new Error('Failed to create organization');
        }
    }

    async update(id: string, updateOrganizationDto: UpdateOrganizationDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Get existing organization
            const existingOrg = await this.prisma.organization.findFirst({
                where: { 
                    id,
                    deleted: false 
                }
            });

            if (!existingOrg) {
                throw new NotFoundException('Organization not found');
            }

            // Check permissions: ADMIN can update all, others can only update their own
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== id) {
                throw new ForbiddenException('You can only update your own organization');
            }

            // Check for conflicts if updating organizationId or email
            if (updateOrganizationDto.organizationId || updateOrganizationDto.organizationEmail) {
                const conflictConditions = [];
                
                if (updateOrganizationDto.organizationId) {
                    conflictConditions.push({ organizationId: updateOrganizationDto.organizationId });
                }
                
                if (updateOrganizationDto.organizationEmail) {
                    conflictConditions.push({ organizationEmail: updateOrganizationDto.organizationEmail });
                }

                const conflictingOrg = await this.prisma.organization.findFirst({
                    where: {
                        OR: conflictConditions,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (conflictingOrg) {
                    throw new ConflictException('Organization with this ID or email already exists');
                }
            }

            // Validate organization type if provided
            if (updateOrganizationDto.organizationTypeId) {
                const orgType = await this.prisma.organizationType.findUnique({
                    where: { id: updateOrganizationDto.organizationTypeId }
                });

                if (!orgType) {
                    throw new NotFoundException('Organization type not found');
                }
            }

            const organization = await this.prisma.organization.update({
                where: { id },
                data: updateOrganizationDto,
                include: {
                    addresses: {
                        where: { deleted: false },
                        orderBy: { addressType: 'asc' }
                    },
                    organizationType: true
                }
            });

            return organization;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update organization ${id}`, error.stack);
            throw new Error('Failed to update organization');
        }
    }

    async remove(id: string, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can delete organizations
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete organizations');
            }

            // Get existing organization
            const existingOrg = await this.prisma.organization.findFirst({
                where: { 
                    id,
                    deleted: false 
                }
            });

            if (!existingOrg) {
                throw new NotFoundException('Organization not found');
            }

            // Check if organization has active users
            const activeUsers = await this.prisma.user.count({
                where: {
                    organizationId: id,
                    deleted: false
                }
            });

            if (activeUsers > 0) {
                throw new BadRequestException('Cannot delete organization with active users');
            }

            // Soft delete organization and its addresses
            await this.prisma.$transaction(async (prisma) => {
                // Soft delete addresses
                await prisma.address.updateMany({
                    where: { organizationId: id },
                    data: { deleted: true }
                });

                // Soft delete organization
                await prisma.organization.update({
                    where: { id },
                    data: { deleted: true }
                });
            });

            return { message: 'Organization deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException || 
                error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to delete organization ${id}`, error.stack);
            throw new Error('Failed to delete organization');
        }
    }

    private async validateOrganizationData(createOrganizationDto: CreateOrganizationDto) {
        // Check if organization type exists
        const orgType = await this.prisma.organizationType.findUnique({
            where: { id: createOrganizationDto.organizationTypeId }
        });

        if (!orgType) {
            throw new NotFoundException('Organization type not found');
        }
    }

    private validateAddresses(addresses: any[]) {
        if (!addresses || addresses.length === 0) {
            throw new BadRequestException('Organization must have at least one address');
        }

        if (addresses.length > 2) {
            throw new BadRequestException('Organization cannot have more than 2 addresses');
        }

        // Check for Rwanda address (mandatory)
        const rwandaAddress = addresses.find(addr => addr.addressType === AddressType.RWANDA);
        if (!rwandaAddress) {
            throw new BadRequestException('Organization must have at least one Rwanda address');
        }

        // Check for duplicate address types
        const addressTypes = addresses.map(addr => addr.addressType);
        const uniqueTypes = new Set(addressTypes);
        if (addressTypes.length !== uniqueTypes.size) {
            throw new BadRequestException('Cannot have duplicate address types');
        }

        // Validate Rwanda address fields
        if (rwandaAddress && (!rwandaAddress.province || !rwandaAddress.district)) {
            throw new BadRequestException('Rwanda address must include province and district');
        }
    }
}
