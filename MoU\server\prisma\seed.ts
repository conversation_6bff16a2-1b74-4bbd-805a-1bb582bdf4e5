import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  const organizationTypes = [
    { typeName: 'Non-Governmental Organization' },
    { typeName: 'Private Company' },
    { typeName: 'Government Agency' },
    { typeName: 'International Organization' },
    { typeName: 'Religious Institution' },
  ];

  for (const type of organizationTypes) {
    await prisma.organizationType.upsert({
      where: { typeName: type.typeName },
      update: {},
      create: {
        typeName: type.typeName,
      },
    });
  }

  console.log('✅ Seeded OrganizationType data.');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
