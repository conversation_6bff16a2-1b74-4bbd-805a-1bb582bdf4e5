/*
  Warnings:

  - You are about to drop the column `organizationId` on the `Organization` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[organizationRegistrationNumber]` on the table `Organization` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `organizationRegistrationNumber` to the `Organization` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "Organization_organizationId_key";

-- AlterTable
ALTER TABLE "Organization" DROP COLUMN "organizationId",
ADD COLUMN     "organizationRegistrationNumber" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Organization_organizationRegistrationNumber_key" ON "Organization"("organizationRegistrationNumber");
