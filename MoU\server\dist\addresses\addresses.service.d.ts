import { PrismaService } from '../prisma/prisma.service';
import { CreateAddressDto, UpdateAddressDto } from './dto';
export declare class AddressesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findByOrganization(organizationId: string): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }[]>;
    findOne(id: string): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    create(createAddressDto: CreateAddressDto, currentUserId: string): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: string, updateAddressDto: UpdateAddressDto, currentUserId: string): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: string, currentUserId: string): Promise<{
        message: string;
    }>;
}
