"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UsersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcryptjs");
const uuid_1 = require("uuid");
let UsersService = UsersService_1 = class UsersService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(UsersService_1.name);
        this.SALT_ROUNDS = 10;
    }
    async findAll(currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can view all users');
            }
            const users = await this.prisma.user.findMany({
                where: { deleted: false },
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' }
            });
            return users.map(user => ({
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }));
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to fetch users', error.stack);
            throw new Error('Failed to fetch users');
        }
    }
    async findOne(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN' && currentUserId !== id) {
                throw new common_1.ForbiddenException('You can only view your own profile');
            }
            const user = await this.prisma.user.findUnique({
                where: { id, deleted: false },
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to fetch user with ID ${id}`, error.stack);
            throw new Error('Failed to fetch user');
        }
    }
    async create(createUserDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create users');
            }
            const existingUser = await this.prisma.user.findUnique({
                where: { email: createUserDto.email }
            });
            if (existingUser) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            if (createUserDto.role === 'PARTNER') {
                throw new common_1.ForbiddenException('ADMIN users cannot create PARTNER users. PARTNER users must self-register with organization data.');
            }
            if (createUserDto.role === 'ADMIN' && createUserDto.organizationId) {
                throw new common_1.BadRequestException('ADMIN users cannot be associated with an organization');
            }
            if (createUserDto.organizationId) {
                const organization = await this.prisma.organization.findUnique({
                    where: { id: createUserDto.organizationId }
                });
                if (!organization) {
                    throw new common_1.NotFoundException('Organization not found');
                }
            }
            const tempPassword = (0, uuid_1.v4)().substring(0, 12);
            const hashedPassword = await bcrypt.hash(tempPassword, this.SALT_ROUNDS);
            const verificationToken = (0, uuid_1.v4)();
            const verificationTokenExpiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000);
            const user = await this.prisma.user.create({
                data: {
                    firstName: createUserDto.firstName,
                    lastName: createUserDto.lastName,
                    email: createUserDto.email,
                    password: hashedPassword,
                    role: createUserDto.role,
                    organizationId: createUserDto.organizationId || null,
                    verificationToken,
                    verificationTokenExpiryTime,
                    emailVerified: false
                },
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                }
            });
            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
                tempPassword
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create user', error.stack);
            throw new Error('Failed to create user');
        }
    }
    async update(id, updateUserDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN' && currentUserId !== id) {
                throw new common_1.ForbiddenException('You can only update your own profile');
            }
            const existingUser = await this.prisma.user.findUnique({
                where: { id, deleted: false }
            });
            if (!existingUser) {
                throw new common_1.NotFoundException('User not found');
            }
            if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
                const emailExists = await this.prisma.user.findUnique({
                    where: { email: updateUserDto.email }
                });
                if (emailExists) {
                    throw new common_1.ConflictException('Email already in use');
                }
            }
            if (updateUserDto.role === 'ADMIN' && updateUserDto.organizationId) {
                throw new common_1.BadRequestException('ADMIN users cannot be associated with an organization');
            }
            if (updateUserDto.organizationId) {
                const organization = await this.prisma.organization.findUnique({
                    where: { id: updateUserDto.organizationId }
                });
                if (!organization) {
                    throw new common_1.NotFoundException('Organization not found');
                }
            }
            const updateData = {};
            if (currentUser.role === 'ADMIN') {
                if (updateUserDto.firstName)
                    updateData.firstName = updateUserDto.firstName;
                if (updateUserDto.lastName)
                    updateData.lastName = updateUserDto.lastName;
                if (updateUserDto.email)
                    updateData.email = updateUserDto.email;
                if (updateUserDto.role)
                    updateData.role = updateUserDto.role;
                if (updateUserDto.organizationId !== undefined)
                    updateData.organizationId = updateUserDto.organizationId;
            }
            else {
                if (updateUserDto.firstName)
                    updateData.firstName = updateUserDto.firstName;
                if (updateUserDto.lastName)
                    updateData.lastName = updateUserDto.lastName;
            }
            const user = await this.prisma.user.update({
                where: { id },
                data: updateData,
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                }
            });
            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to update user with ID ${id}`, error.stack);
            throw new Error('Failed to update user');
        }
    }
    async remove(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete users');
            }
            const existingUser = await this.prisma.user.findUnique({
                where: { id, deleted: false }
            });
            if (!existingUser) {
                throw new common_1.NotFoundException('User not found');
            }
            if (id === currentUserId) {
                throw new common_1.ForbiddenException('You cannot delete your own account');
            }
            await this.prisma.user.update({
                where: { id },
                data: { deleted: true }
            });
            return { message: 'User deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete user with ID ${id}`, error.stack);
            throw new Error('Failed to delete user');
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = UsersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UsersService);
//# sourceMappingURL=users.service.js.map