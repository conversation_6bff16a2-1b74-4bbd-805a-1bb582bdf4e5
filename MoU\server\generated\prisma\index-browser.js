
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  organizationName: 'organizationName',
  organizationRegistrationNumber: 'organizationRegistrationNumber',
  organizationPhoneNumber: 'organizationPhoneNumber',
  organizationEmail: 'organizationEmail',
  organizationWebsite: 'organizationWebsite',
  homeCountryRepresentative: 'homeCountryRepresentative',
  rwandaRepresentative: 'rwandaRepresentative',
  organizationRgbNumber: 'organizationRgbNumber',
  organizationTypeId: 'organizationTypeId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.OrganizationTypeScalarFieldEnum = {
  id: 'id',
  typeName: 'typeName',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.PartyScalarFieldEnum = {
  id: 'id',
  partyId: 'partyId',
  name: 'name',
  responsibilityId: 'responsibilityId',
  organizationId: 'organizationId',
  objectiveId: 'objectiveId',
  goalId: 'goalId',
  signatory: 'signatory',
  position: 'position',
  duration: 'duration',
  reasonForExtendedDuration: 'reasonForExtendedDuration',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ResponsibilityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ObjectiveScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.GoalScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  documentId: 'documentId',
  name: 'name',
  description: 'description',
  organizationId: 'organizationId',
  documentTypeId: 'documentTypeId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DocumentTypeScalarFieldEnum = {
  id: 'id',
  typeName: 'typeName',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ActivityScalarFieldEnum = {
  id: 'id',
  activityId: 'activityId',
  name: 'name',
  description: 'description',
  projectId: 'projectId',
  startDate: 'startDate',
  endDate: 'endDate',
  implementer: 'implementer',
  implementerUnit: 'implementerUnit',
  fiscalYear: 'fiscalYear',
  domainOfInterventionId: 'domainOfInterventionId',
  inputId: 'inputId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DomainOfInterventionScalarFieldEnum = {
  id: 'id',
  domainOfInterventionId: 'domainOfInterventionId',
  domainName: 'domainName',
  subDomainOfInterventionId: 'subDomainOfInterventionId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.SubDomainOfInterventionScalarFieldEnum = {
  id: 'id',
  subDomainOfInterventionId: 'subDomainOfInterventionId',
  subDomainName: 'subDomainName',
  domainFunctionId: 'domainFunctionId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DomainFunctionScalarFieldEnum = {
  id: 'id',
  domainFunctionId: 'domainFunctionId',
  domainFunctionName: 'domainFunctionName',
  subDomainFunctionId: 'subDomainFunctionId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.SubDomainFunctionScalarFieldEnum = {
  id: 'id',
  subDomainFunctionId: 'subDomainFunctionId',
  domainFunctionName: 'domainFunctionName',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.InputScalarFieldEnum = {
  id: 'id',
  inputId: 'inputId',
  name: 'name',
  inputSubclassId: 'inputSubclassId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.InputSubclassScalarFieldEnum = {
  id: 'id',
  inputSubclassId: 'inputSubclassId',
  name: 'name',
  budget: 'budget',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  name: 'name',
  description: 'description',
  duration: 'duration',
  budgetTypeId: 'budgetTypeId',
  fundingUnitId: 'fundingUnitId',
  fundingSourceId: 'fundingSourceId',
  organizationId: 'organizationId',
  projectDocumentId: 'projectDocumentId',
  mouApplicationId: 'mouApplicationId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.BudgetTypeScalarFieldEnum = {
  id: 'id',
  typeName: 'typeName',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.FundingUnitScalarFieldEnum = {
  id: 'id',
  unitName: 'unitName',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.FundingSourceScalarFieldEnum = {
  id: 'id',
  sourceName: 'sourceName',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.MouScalarFieldEnum = {
  id: 'id',
  mouId: 'mouId',
  partyId: 'partyId',
  createAt: 'createAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.AddressScalarFieldEnum = {
  id: 'id',
  addressType: 'addressType',
  country: 'country',
  province: 'province',
  district: 'district',
  sector: 'sector',
  cell: 'cell',
  village: 'village',
  street: 'street',
  avenue: 'avenue',
  poBox: 'poBox',
  postalCode: 'postalCode',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  password: 'password',
  emailVerified: 'emailVerified',
  verifiedAt: 'verifiedAt',
  verificationToken: 'verificationToken',
  verificationTokenExpiryTime: 'verificationTokenExpiryTime',
  refreshToken: 'refreshToken',
  passwordResetToken: 'passwordResetToken',
  passwordResetExpires: 'passwordResetExpires',
  invitationToken: 'invitationToken',
  invitationExpires: 'invitationExpires',
  invitedBy: 'invitedBy',
  role: 'role',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ApprovalStepScalarFieldEnum = {
  id: 'id',
  mouApplicationId: 'mouApplicationId',
  reviewerId: 'reviewerId',
  role: 'role',
  projectId: 'projectId',
  status: 'status',
  comment: 'comment',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MouApplicationScalarFieldEnum = {
  id: 'id',
  mouApplicationId: 'mouApplicationId',
  mouId: 'mouId',
  responsibility: 'responsibility',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.AddressType = exports.$Enums.AddressType = {
  HEADQUARTERS: 'HEADQUARTERS',
  RWANDA: 'RWANDA'
};

exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  PARTNER: 'PARTNER',
  COORDINATOR: 'COORDINATOR',
  LEGAL: 'LEGAL',
  TECHNICAL_EXPERT: 'TECHNICAL_EXPERT',
  HOD: 'HOD',
  PS: 'PS',
  MINISTER: 'MINISTER'
};

exports.ApprovalStatusType = exports.$Enums.ApprovalStatusType = {
  PENDING: 'PENDING',
  RECOMMEND_FOR_APPROVAL: 'RECOMMEND_FOR_APPROVAL',
  RECOMMEND_FOR_MODIFICATION: 'RECOMMEND_FOR_MODIFICATION',
  REJECT: 'REJECT',
  APPROVE: 'APPROVE'
};

exports.Prisma.ModelName = {
  Organization: 'Organization',
  OrganizationType: 'OrganizationType',
  Party: 'Party',
  Responsibility: 'Responsibility',
  Objective: 'Objective',
  Goal: 'Goal',
  Document: 'Document',
  DocumentType: 'DocumentType',
  Activity: 'Activity',
  DomainOfIntervention: 'DomainOfIntervention',
  SubDomainOfIntervention: 'SubDomainOfIntervention',
  DomainFunction: 'DomainFunction',
  SubDomainFunction: 'SubDomainFunction',
  Input: 'Input',
  InputSubclass: 'InputSubclass',
  Project: 'Project',
  BudgetType: 'BudgetType',
  FundingUnit: 'FundingUnit',
  FundingSource: 'FundingSource',
  Mou: 'Mou',
  Address: 'Address',
  User: 'User',
  ApprovalStep: 'ApprovalStep',
  MouApplication: 'MouApplication'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
