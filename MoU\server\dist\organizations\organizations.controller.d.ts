import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from './dto';
export declare class OrganizationsController {
    private readonly organizationsService;
    constructor(organizationsService: OrganizationsService);
    findAll(req: any): Promise<({
        organizationType: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createAt: Date;
            typeName: string;
        };
        addresses: {
            addressType: import("generated/prisma").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
            organizationId: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        createAt: Date;
    })[]>;
    findOne(id: string, req: any): Promise<{
        organizationType: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createAt: Date;
            typeName: string;
        };
        addresses: {
            addressType: import("generated/prisma").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
            organizationId: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        createAt: Date;
    }>;
    create(createOrganizationDto: CreateOrganizationDto, req: any): Promise<{
        organizationType: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createAt: Date;
            typeName: string;
        };
        addresses: {
            addressType: import("generated/prisma").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
            organizationId: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        createAt: Date;
    }>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto, req: any): Promise<{
        organizationType: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createAt: Date;
            typeName: string;
        };
        addresses: {
            addressType: import("generated/prisma").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
            organizationId: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        createAt: Date;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
