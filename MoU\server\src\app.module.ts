import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MailerModule } from '@nestjs-modules/mailer';
import { join } from 'path';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { AuthModule } from './auth/auth.module';
import { PrismaModule } from './prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { EmailModule } from './email/email.module';
import { APP_GUARD } from '@nestjs/core';
import { JwtGuard } from './auth/guard/jwt-auth.guard';
import { NotificationsModule } from './notifications/notifications.module';
import { UsersModule } from './users/users.module';
import { OrganizationTypesModule } from './organization-types/organization-types.module';
import { OrganizationsModule } from './organizations/organizations.module';
import { AddressesModule } from './addresses/addresses.module';
import { BudgetTypesModule } from './budget-types/budget-types.module';
import { FundingSourcesModule } from './funding-sources/funding-sources.module';
import { FundingUnitsModule } from './funding-units/funding-units.module';
import { ServeStaticModule } from '@nestjs/serve-static';

@Module({
  imports: [
    MailerModule.forRoot({
      transport: {
        host: 'localhost',
        port: 1025,
        ignoreTLS: true,
        secure: false,
      },
      defaults: {
        from: '"MoU Management System" <<EMAIL>>',
      },
      template: {
        dir: join(__dirname, 'email/templates'),
        adapter: new HandlebarsAdapter(),
        options: {
          strict: true,
        },
      },
    }),
    ConfigModule.forRoot({
      cache: true,
      isGlobal: true
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads'),
      serveRoot: '/uploads',
    }),
    PrismaModule,
    AuthModule,
    EmailModule,
    UsersModule,
    OrganizationTypesModule,
    OrganizationsModule,
    AddressesModule,
    BudgetTypesModule,
    FundingSourcesModule,
    FundingUnitsModule,
    NotificationsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtGuard
    }
  ],
})
export class AppModule { }
