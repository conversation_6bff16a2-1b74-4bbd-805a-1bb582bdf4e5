import { AddressesService } from './addresses.service';
import { CreateAddressDto, UpdateAddressDto } from './dto';
export declare class AddressesController {
    private readonly addressesService;
    constructor(addressesService: AddressesService);
    findByOrganization(organizationId: string): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }[]>;
    findOne(id: string): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    create(createAddressDto: CreateAddressDto, req: any): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: string, updateAddressDto: UpdateAddressDto, req: any): Promise<{
        addressType: import("generated/prisma").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
        organizationId: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
